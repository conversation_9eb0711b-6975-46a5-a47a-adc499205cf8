import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { createClient } from '@/utils/supabase/client'
import { api } from '../services/api'

interface TicketFilters {
  status?: string;
  myTickets?: boolean;
}

export function useTickets(filters: TicketFilters = {}) {
  return useQuery({
    queryKey: ['tickets', filters],
    queryFn: () => api.getTickets(filters),
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: 2
  })
}

export function useTicket(id: string | null) {
  const queryClient = useQueryClient()
  const supabase = createClient()

  const query = useQuery({
    queryKey: ['tickets', id],
    queryFn: () => api.getTicket(id!),
    enabled: !!id,
    refetchInterval: 10000, // Refresh every 10 seconds
    retry: 2
  })

  // Set up realtime subscription for this specific ticket
  useEffect(() => {
    if (!id) return

    const channel = supabase
      .channel(`ticket-${id}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tickets',
          filter: `id=eq.${id}`
        },
        (payload) => {
          console.log('Ticket updated:', payload)
          // Invalidate and refetch the specific ticket
          queryClient.invalidateQueries({ queryKey: ['tickets', id] })
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ticket_messages',
          filter: `ticket_id=eq.${id}`
        },
        (payload) => {
          console.log('Ticket message updated:', payload)
          // Invalidate and refetch the specific ticket to get updated comments
          queryClient.invalidateQueries({ queryKey: ['tickets', id] })
          queryClient.invalidateQueries({ queryKey: ['ticket-messages', id] })
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ticket_status_history',
          filter: `ticket_id=eq.${id}`
        },
        (payload) => {
          console.log('Ticket status history updated:', payload)
          // Invalidate and refetch the specific ticket
          queryClient.invalidateQueries({ queryKey: ['tickets', id] })
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [id, queryClient, supabase])

  return query
}

export function useCreateTicket() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: api.createTicket,
    onMutate: async (newTicket) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['tickets'] })
      
      // Snapshot the previous value
      const previousTickets = queryClient.getQueryData(['tickets'])

      // Optimistically update to the new value
      queryClient.setQueryData(['tickets'], (old: any) => ({
        ...old,
        data: [
          { 
            ...newTicket, 
            id: 'temp-id', 
            status: 'open',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          ...(old?.data || [])
        ]
      }))

      return { previousTickets }
    },
    onError: (err, _, context) => {
      // Revert the optimistic update
      if (context?.previousTickets) {
        queryClient.setQueryData(['tickets'], context.previousTickets)
      }
      return err
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['tickets'] })
    }
  })
}

export function useUpdateTicket() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, ...data }: { id: string; [key: string]: any }) => 
      api.updateTicket(id, data),
    onMutate: async ({ id, ...updateData }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['tickets', id] })
      
      // Snapshot the previous value
      const previousTicket = queryClient.getQueryData(['tickets', id])

      // Optimistically update to the new value
      queryClient.setQueryData(['tickets', id], (old: any) => ({
        ...old,
        ...updateData,
        updated_at: new Date().toISOString()
      }))

      return { previousTicket, id }
    },
    onError: (err, _, context) => {
      // Revert the optimistic update
      if (context?.previousTicket && context?.id) {
        queryClient.setQueryData(['tickets', context.id], context.previousTicket)
      }
      return err
    },
    onSettled: (_, __, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['tickets', id] })
      queryClient.invalidateQueries({ queryKey: ['tickets'] })
    }
  })
}

export function useUpdateTicketStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) => 
      api.updateTicketStatus(id, status),
    onMutate: async ({ id, status }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['tickets', id] })
      
      // Snapshot the previous value
      const previousTicket = queryClient.getQueryData(['tickets', id])

      // Optimistically update to the new value
      queryClient.setQueryData(['tickets', id], (old: any) => ({
        ...old,
        status,
        updated_at: new Date().toISOString()
      }))

      return { previousTicket, id }
    },
    onError: (err, _, context) => {
      // Revert the optimistic update
      if (context?.previousTicket && context?.id) {
        queryClient.setQueryData(['tickets', context.id], context.previousTicket)
      }
      return err
    },
    onSettled: (_, __, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['tickets', id] })
      queryClient.invalidateQueries({ queryKey: ['tickets'] })
    }
  })
}

export function useTicketMessages(ticketId: string | null, page?: number, limit?: number) {
  return useQuery({
    queryKey: ['ticket-messages', ticketId, page, limit],
    queryFn: () => api.getTicketMessages(ticketId!, page, limit),
    enabled: !!ticketId,
    refetchInterval: 15000, // Refresh every 15 seconds
    retry: 2
  })
}

export function useAddMessage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ ticketId, body }: { ticketId: string; body: string }) => 
      api.addTicketMessage(ticketId, body),
    onMutate: async ({ ticketId, body }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['ticket-messages', ticketId] })
      
      // Snapshot the previous value
      const previousMessages = queryClient.getQueryData(['ticket-messages', ticketId])

      // Optimistically update to the new value
      queryClient.setQueryData(['ticket-messages', ticketId], (old: any) => [
        ...(old || []),
        {
          id: 'temp-id',
          body,
          created_at: new Date().toISOString(),
          author: { id: 'current-user', email: '<EMAIL>' } // This will be replaced by real data
        }
      ])

      return { previousMessages, ticketId }
    },
    onError: (err, _, context) => {
      // Revert the optimistic update
      if (context?.previousMessages && context?.ticketId) {
        queryClient.setQueryData(['ticket-messages', context.ticketId], context.previousMessages)
      }
      return err
    },
    onSettled: (_, __, { ticketId }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['ticket-messages', ticketId] })
      queryClient.invalidateQueries({ queryKey: ['tickets', ticketId] })
    }
  })
}
